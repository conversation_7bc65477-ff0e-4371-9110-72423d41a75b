# Python 缓存
__pycache__/
*.py[cod]
*$py.class

# Node.js 模块缓存
node_modules/
**/node_modules/

# Next.js 构建目录
.next/
**/.next/

# Next.js 静态导出
out/
**/out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# VSCode 配置
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
# 忽略 VSCode Python 导入助手生成的文件
.vscode/PythonImportHelper-v2-Completion.json

# IDE 配置
.idea/
*.swp
*.swo

# 日志文件
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
*.log

# 依赖锁文件（根据项目需要选择保留哪个）
package-lock.json
yarn.lock
pnpm-lock.yaml

# 操作系统临时文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript 编译缓存
*.tsbuildinfo
.tscache/

# 构建产物
dist/
build/
coverage/

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率
coverage/
.nyc_output/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 可选：忽略特定的二进制文件和可执行文件
*.exe
*.dll
*.so
*.dylib

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*~

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 可选：忽略 PDF 和临时文档（根据你的需要）
# *.pdf
# *.doc
# *.docx
