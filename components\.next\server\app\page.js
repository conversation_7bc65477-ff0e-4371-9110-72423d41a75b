/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjcmVhdGUtbGxsYW1hJTVDJTVDcHlsbGFtYWluZGV4JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNyZWF0ZS1sbGxhbWFcXFxccHlsbGFtYWluZGV4XFxcXGNvbXBvbmVudHNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjcmVhdGUtbGxsYW1hJTVDJTVDcHlsbGFtYWluZGV4JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNyZWF0ZS1sbGxhbWFcXFxccHlsbGFtYWluZGV4XFxcXGNvbXBvbmVudHNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/CitationTooltip.tsx":
/*!********************************************!*\
  !*** ./app/components/CitationTooltip.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CitationTooltip: () => (/* binding */ CitationTooltip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./app/components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ CitationTooltip auto */ \n\n\nfunction CitationTooltip({ citationId, children }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [citationData, setCitationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const loadCitationData = async ()=>{\n        if (citationData || isLoading) return;\n        setIsLoading(true);\n        try {\n            const response = await fetch(`/api/citation/${citationId}`);\n            if (!response.ok) throw new Error(\"Failed to load citation\");\n            const data = await response.json();\n            setCitationData({\n                id: data.id || citationId,\n                title: data.metadata?.file_name || `Document ${citationId.substring(0, 8)}...`,\n                content: data.text || data.content || \"内容不可用\",\n                metadata: data.metadata\n            });\n        } catch (error) {\n            console.error(\"Error loading citation:\", error);\n            setCitationData({\n                id: citationId,\n                title: \"Error\",\n                content: \"Failed to load citation content\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        const rect = triggerRef.current?.getBoundingClientRect();\n        if (rect) {\n            setPosition({\n                x: rect.left + rect.width / 2,\n                y: rect.top - 10\n            });\n        }\n        setIsVisible(true);\n        loadCitationData();\n    };\n    const handleMouseLeave = ()=>{\n        setIsVisible(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CitationTooltip.useEffect\": ()=>{\n            if (isVisible && tooltipRef.current) {\n                const tooltip = tooltipRef.current;\n                const rect = tooltip.getBoundingClientRect();\n                const viewportWidth = window.innerWidth;\n                const viewportHeight = window.innerHeight;\n                let adjustedX = position.x - rect.width / 2;\n                let adjustedY = position.y - rect.height;\n                // 确保tooltip不超出视口\n                if (adjustedX < 10) adjustedX = 10;\n                if (adjustedX + rect.width > viewportWidth - 10) {\n                    adjustedX = viewportWidth - rect.width - 10;\n                }\n                if (adjustedY < 10) adjustedY = position.y + 30;\n                tooltip.style.left = `${adjustedX}px`;\n                tooltip.style.top = `${adjustedY}px`;\n            }\n        }\n    }[\"CitationTooltip.useEffect\"], [\n        isVisible,\n        position,\n        citationData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                ref: triggerRef,\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                className: \"citation-number\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: tooltipRef,\n                className: \"citation-tooltip fixed z-50\",\n                style: {\n                    left: position.x,\n                    top: position.y\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                            size: \"sm\",\n                            className: \"text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 13\n                }, this) : citationData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-gray-900 mb-2\",\n                            children: citationData.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-700 text-sm leading-relaxed\",\n                            children: citationData.content.length > 300 ? `${citationData.content.substring(0, 300)}...` : citationData.content\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Error loading citation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CitationTooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/CustomChatMessage.tsx":
/*!**********************************************!*\
  !*** ./app/components/CustomChatMessage.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomChatMessage: () => (/* binding */ CustomChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @llamaindex/chat-ui */ \"(ssr)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var _CitationTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CitationTooltip */ \"(ssr)/./app/components/CitationTooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomChatMessage auto */ \n\n\nfunction CustomChatMessage({ message, isLoading }) {\n    // 处理引用标记的函数\n    const processContent = (content)=>{\n        // 查找所有引用标记\n        const citationRegex = /\\[citation:(.*?)\\]/g;\n        const citations = [];\n        let match;\n        while((match = citationRegex.exec(content)) !== null){\n            citations.push(match[1]);\n        }\n        // 替换引用标记为带tooltip的序号\n        let processedContent = content;\n        citations.forEach((citationId, index)=>{\n            const citationNumber = index + 1;\n            const citationPattern = new RegExp(`\\\\[citation:${citationId.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")}\\\\]`, \"g\");\n            processedContent = processedContent.replace(citationPattern, `<citation-placeholder data-citation-id=\"${citationId}\" data-citation-number=\"${citationNumber}\"></citation-placeholder>`);\n        });\n        return processedContent;\n    };\n    // 渲染处理后的内容\n    const renderContent = (content)=>{\n        const processedContent = processContent(content);\n        // 分割内容并处理引用占位符\n        const parts = processedContent.split(/(<citation-placeholder[^>]*><\\/citation-placeholder>)/g);\n        return parts.map((part, index)=>{\n            const citationMatch = part.match(/<citation-placeholder data-citation-id=\"([^\"]*)\" data-citation-number=\"([^\"]*)\"><\\/citation-placeholder>/);\n            if (citationMatch) {\n                const [, citationId, citationNumber] = citationMatch;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CitationTooltip__WEBPACK_IMPORTED_MODULE_1__.CitationTooltip, {\n                    citationId: citationId,\n                    children: [\n                        \"[\",\n                        citationNumber,\n                        \"]\"\n                    ]\n                }, `citation-${index}`, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this);\n            }\n            return part;\n        });\n    };\n    // 如果是助手消息且包含引用，使用自定义渲染\n    if (message.role === \"assistant\" && message.content.includes(\"[citation:\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"} mb-4 chat-message`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `max-w-[80%] rounded-lg px-4 py-3 ${message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground border border-border\"} shadow-sm`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none dark:prose-invert\",\n                        children: renderContent(message.content)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-3 space-x-2 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"正在思考...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    // 对于其他消息，使用默认的ChatMessage组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n        message: message,\n        isLoading: isLoading\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n        lineNumber: 102,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CustomChatMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./app/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center p-6 bg-red-50 border border-red-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold text-red-800 mb-2\",\n                children: \"Something went wrong\"\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-600 text-sm mb-4 text-center\",\n                children: error?.message || 'An unexpected error occurred'\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: resetError,\n                className: \"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\",\n                children: \"Try again\"\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner auto */ \nfunction LoadingSpinner({ size = 'md', className = '' }) {\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} border-2 border-current border-t-transparent rounded-full animate-spin ${className}`\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQU9PLFNBQVNBLGVBQWUsRUFBRUMsT0FBTyxJQUFJLEVBQUVDLFlBQVksRUFBRSxFQUF1QjtJQUNqRixNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsR0FBR0MsV0FBVyxDQUFDRixLQUFLLENBQUMsd0VBQXdFLEVBQUVDLFdBQVc7Ozs7OztBQUU5SCIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcYXBwXFxjb21wb25lbnRzXFxMb2FkaW5nU3Bpbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmludGVyZmFjZSBMb2FkaW5nU3Bpbm5lclByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZCcsIGNsYXNzTmFtZSA9ICcnIH06IExvYWRpbmdTcGlubmVyUHJvcHMpIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICd3LTQgaC00JyxcbiAgICBtZDogJ3ctNiBoLTYnLCBcbiAgICBsZzogJ3ctOCBoLTgnXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gYm9yZGVyLTIgYm9yZGVyLWN1cnJlbnQgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiAke2NsYXNzTmFtZX1gfSAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZ1NwaW5uZXIiLCJzaXplIiwiY2xhc3NOYW1lIiwic2l6ZUNsYXNzZXMiLCJzbSIsIm1kIiwibGciLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @llamaindex/chat-ui */ \"(ssr)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ai/react */ \"(ssr)/./node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var _components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/CustomChatMessage */ \"(ssr)/./app/components/CustomChatMessage.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(ssr)/./app/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatPage() {\n    const handler = (0,ai_react__WEBPACK_IMPORTED_MODULE_3__.useChat)({\n        api: \"/api/chat\",\n        onError: {\n            \"ChatPage.useChat[handler]\": (error)=>{\n                console.error(\"Chat error:\", error);\n            }\n        }[\"ChatPage.useChat[handler]\"],\n        // 自定义请求体格式以匹配FastAPI后端\n        body: {\n            id: `chat-${Date.now()}`\n        },\n        initialMessages: [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n            }\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatSection, {\n                handler: handler,\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatMessages, {\n                        messageRenderer: (message, isLoading)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_1__.CustomChatMessage, {\n                                message: message,\n                                isLoading: isLoading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bf3811a20115\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZjM4MTFhMjAxMTVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _llamaindex_chat_ui_styles_markdown_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @llamaindex/chat-ui/styles/markdown.css */ \"(rsc)/./node_modules/@llamaindex/chat-ui/dist/styles/markdown.css\");\n/* harmony import */ var _llamaindex_chat_ui_styles_pdf_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @llamaindex/chat-ui/styles/pdf.css */ \"(rsc)/./node_modules/@llamaindex/chat-ui/dist/styles/pdf.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _src_components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/message-square.mjs\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"PyLlamaIndex Chat\",\n    description: \"AI-powered chat interface with document citations\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-border bg-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-foreground\",\n                                        children: \"PyLlamaIndex Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 36,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"聊天\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/documents\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 42,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"文档管理\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto h-[calc(100vh-73px)]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\create-lllama\\pyllamaindex\\components\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _src_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? \"span\" : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 44,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@llamaindex","vendor-chunks/tailwind-merge","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/highlight.js","vendor-chunks/@mdxeditor","vendor-chunks/react-markdown","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/zod-to-json-schema","vendor-chunks/@radix-ui","vendor-chunks/@lexical","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/micromark-extension-gfm","vendor-chunks/hast-util-from-parse5","vendor-chunks/property-information","vendor-chunks/prismjs","vendor-chunks/parse5","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/@codemirror","vendor-chunks/zod","vendor-chunks/mdast-util-gfm","vendor-chunks/micromark","vendor-chunks/micromark-extension-math","vendor-chunks/stringify-entities","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/@uiw","vendor-chunks/@lezer","vendor-chunks/@babel","vendor-chunks/react-remove-scroll","vendor-chunks/prop-types","vendor-chunks/swr","vendor-chunks/remark","vendor-chunks/mdast-util-math","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/mdast-util-definitions","vendor-chunks/entities","vendor-chunks/@floating-ui","vendor-chunks/vfile","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-mdx-jsx","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/hast-util-from-html","vendor-chunks/@ai-sdk","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/debug","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/use-callback-ref","vendor-chunks/uvu","vendor-chunks/unist-util-visit-parents","vendor-chunks/micromark-util-subtokenize","vendor-chunks/estree-util-visit","vendor-chunks/dequal","vendor-chunks/use-sync-external-store","vendor-chunks/use-sidecar","vendor-chunks/style-to-object","vendor-chunks/react-is","vendor-chunks/zwitch","vendor-chunks/web-namespaces","vendor-chunks/w3c-keyname","vendor-chunks/vfile-message","vendor-chunks/vfile-location","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-remove-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-position-from-estree","vendor-chunks/unist-util-is","vendor-chunks/unist-util-generated","vendor-chunks/unist-util-find-after","vendor-chunks/unified","vendor-chunks/tslib","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/style-mod","vendor-chunks/space-separated-tokens","vendor-chunks/remark-stringify","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-math","vendor-chunks/remark-gfm","vendor-chunks/rehype-katex","vendor-chunks/react-hook-form","vendor-chunks/parse-entities","vendor-chunks/nanoid","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-events-to-acorn","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-mdx-expression","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-mdx-md","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-mdx-jsx","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/lexical","vendor-chunks/kleur","vendor-chunks/katex","vendor-chunks/is-plain-obj","vendor-chunks/is-hexadecimal","vendor-chunks/is-decimal","vendor-chunks/is-alphanumerical","vendor-chunks/is-alphabetical","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-text","vendor-chunks/hast-util-parse-selector","vendor-chunks/hast-util-is-element","vendor-chunks/hast-util-from-html-isomorphic","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/diff","vendor-chunks/devlop","vendor-chunks/decode-named-character-reference","vendor-chunks/crelt","vendor-chunks/compute-scroll-into-view","vendor-chunks/comma-separated-tokens","vendor-chunks/character-reference-invalid","vendor-chunks/character-entities","vendor-chunks/character-entities-legacy","vendor-chunks/character-entities-html4","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/ai","vendor-chunks/@marijn","vendor-chunks/classnames","vendor-chunks/throttleit","vendor-chunks/supports-color","vendor-chunks/secure-json-parse","vendor-chunks/object-assign","vendor-chunks/ms","vendor-chunks/is-buffer","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/get-nonce","vendor-chunks/extend","vendor-chunks/downshift","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();