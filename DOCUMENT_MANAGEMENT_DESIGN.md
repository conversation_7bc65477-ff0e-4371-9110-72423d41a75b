# 文档管理页面设计方案

## 📋 项目概述

基于当前的 PyLlamaIndex 项目架构，设计一个优雅简单的文档管理页面，集成到现有的 Next.js 前端中。

## 🎯 功能需求

### 1. 文档展示
- 在可滚动窗口中按上传时间倒序显示文档
- 最新文档显示在右上角位置
- 鼠标悬浮显示完整文档名称和上传日期
- 支持文档图标/缩略图显示

### 2. 批量上传
- 支持单个或多个文档同时上传
- 实时处理进度显示
- 处理完成后用户通知
- 失败文档信息展示

### 3. 文档删除
- 每个文档右上角删除按钮
- 删除确认对话框
- 同时删除数据库和文件系统数据

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 15 + React 19 + TypeScript
- **样式**: Tailwind CSS + shadcn/ui
- **状态管理**: React hooks (useState, useEffect)
- **文件上传**: HTML5 File API + fetch
- **UI组件**: 基于现有的 shadcn/ui 组件

### 后端API设计
- **文档列表**: `GET /api/documents`
- **文档上传**: `POST /api/documents/upload`
- **文档删除**: `DELETE /api/documents/{doc_id}`
- **处理状态**: `GET /api/documents/status/{task_id}`

## 📁 文件结构

```
components/
├── app/
│   ├── documents/                 # 新增文档管理页面
│   │   └── page.tsx              # 文档管理主页面
│   ├── components/
│   │   ├── DocumentCard.tsx      # 文档卡片组件
│   │   ├── DocumentUpload.tsx    # 文档上传组件
│   │   ├── DocumentGrid.tsx      # 文档网格布局
│   │   └── DeleteConfirm.tsx     # 删除确认对话框
│   └── layout.tsx                # 更新导航菜单
```

## 🎨 UI设计方案

### 1. 页面布局
```
┌─────────────────────────────────────────────────────────┐
│ 📄 文档管理                    [批量上传] [返回聊天]      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                文档网格区域                         │ │
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                   │ │
│  │  │ 📄  │ │ 📄  │ │ 📄  │ │ 📄  │ ← 最新文档        │ │
│  │  │ [×] │ │ [×] │ │ [×] │ │ [×] │                   │ │
│  │  └─────┘ └─────┘ └─────┘ └─────┘                   │ │
│  │  ┌─────┐ ┌─────┐ ┌─────┐                           │ │
│  │  │ 📄  │ │ 📄  │ │ 📄  │                           │ │
│  │  │ [×] │ │ [×] │ │ [×] │                           │ │
│  │  └─────┘ └─────┘ └─────┘                           │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 2. 文档卡片设计
```
┌─────────────────┐
│ 📄              [×]│ ← 删除按钮
│                   │
│   文档图标         │
│                   │
│ 文档名称.txt       │
│ 2024-01-15        │ ← 上传日期
└─────────────────┘
```

### 3. 上传进度界面
```
┌─────────────────────────────────────┐
│ 📤 正在上传文档...                   │
├─────────────────────────────────────┤
│ ✅ document1.txt - 已完成            │
│ ⏳ document2.pdf - 处理中...         │
│ ❌ document3.docx - 失败             │
│                                     │
│ [关闭] [重试失败项]                  │
└─────────────────────────────────────┘
```

## 🔧 核心组件实现

### 1. DocumentCard 组件
```typescript
interface DocumentCardProps {
  document: {
    id: string;
    name: string;
    uploadDate: string;
    type: string;
    size: number;
  };
  onDelete: (id: string) => void;
}
```

### 2. DocumentUpload 组件
```typescript
interface DocumentUploadProps {
  onUploadComplete: (results: UploadResult[]) => void;
  onUploadProgress: (progress: UploadProgress) => void;
}
```

### 3. DocumentGrid 组件
```typescript
interface DocumentGridProps {
  documents: Document[];
  onDocumentDelete: (id: string) => void;
  loading: boolean;
}
```

## 🚀 后端API实现

### 1. 文档列表API
```python
@app.get("/api/documents")
async def get_documents():
    """获取所有文档列表，按上传时间倒序"""
    # 从SQLite数据库查询文档信息
    # 返回文档列表
```

### 2. 文档上传API
```python
@app.post("/api/documents/upload")
async def upload_documents(files: List[UploadFile]):
    """批量上传文档并处理"""
    # 1. 保存文件到data目录
    # 2. 使用LlamaIndex处理文档
    # 3. 存储到SQLite和ChromaDB
    # 4. 返回处理结果
```

### 3. 文档删除API
```python
@app.delete("/api/documents/{doc_id}")
async def delete_document(doc_id: str):
    """删除文档及相关数据"""
    # 1. 从ChromaDB删除向量数据
    # 2. 从SQLite删除文档记录
    # 3. 从data目录删除文件
    # 4. 返回删除结果
```

## 📊 数据库设计

### SQLite表结构扩展
```sql
-- 扩展现有documents表
ALTER TABLE documents ADD COLUMN file_name TEXT;
ALTER TABLE documents ADD COLUMN file_size INTEGER;
ALTER TABLE documents ADD COLUMN file_type TEXT;
ALTER TABLE documents ADD COLUMN upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 创建索引
CREATE INDEX idx_upload_date ON documents(upload_date DESC);
```

## 🎯 实现步骤

### 阶段1: 后端API开发
1. 扩展SQLite数据库表结构
2. 实现文档列表查询API
3. 实现文档上传处理API
4. 实现文档删除API
5. 集成LlamaIndex文档处理流程

### 阶段2: 前端组件开发
1. 创建基础UI组件
2. 实现文档网格布局
3. 开发文件上传功能
4. 添加删除确认对话框
5. 集成进度显示和错误处理

### 阶段3: 集成测试
1. 端到端功能测试
2. 文件上传性能测试
3. 并发处理测试
4. 错误处理测试

### 阶段4: 优化完善
1. UI/UX优化
2. 性能优化
3. 错误处理完善
4. 用户体验改进

## 🔒 安全考虑

1. **文件类型验证**: 限制上传文件类型
2. **文件大小限制**: 防止大文件攻击
3. **路径安全**: 防止路径遍历攻击
4. **权限控制**: 确保只能删除自己的文档
5. **输入验证**: 验证所有用户输入

## 📈 性能优化

1. **分页加载**: 大量文档时分页显示
2. **虚拟滚动**: 优化长列表性能
3. **缓存策略**: 缓存文档列表数据
4. **异步处理**: 文档处理使用后台任务
5. **进度反馈**: 实时显示处理进度

## 🎨 样式主题

基于现有的shadcn/ui主题，保持与聊天界面一致的设计风格：
- 使用相同的颜色方案
- 保持一致的圆角和阴影
- 统一的字体和间距
- 响应式设计适配移动端

## 📝 总结

这个方案充分利用了现有的技术栈和架构，最小化开发复杂度，同时提供完整的文档管理功能。实现后将与现有的聊天功能形成完整的RAG应用生态。

### 核心优势
- 与现有架构无缝集成
- 使用成熟的技术栈
- 优雅的用户体验设计
- 完整的错误处理机制
- 良好的性能优化策略

### 预期效果
- 用户可以方便地管理文档
- 支持批量操作提高效率
- 实时反馈增强用户体验
- 安全可靠的数据管理
- 响应式设计适配各种设备
